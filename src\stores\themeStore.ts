import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

export type Theme = 'light' | 'dark' | 'system';
export type ThemeVariant = 'default' | 'high-contrast' | 'colorblind-friendly';

export interface ThemeColors {
  // Core brand colors
  primary: string;
  primaryForeground: string;
  secondary: string;
  secondaryForeground: string;
  accent: string;
  accentForeground: string;
  neutral: string;

  // Semantic colors
  success: string;
  successForeground: string;
  warning: string;
  warningForeground: string;
  error: string;
  errorForeground: string;
  info: string;
  infoForeground: string;

  // Background colors
  background: string;
  foreground: string;
  surface: string;
  surfaceSecondary: string;
  surfaceTertiary: string;

  // Text colors
  text: string;
  textSecondary: string;
  textMuted: string;
  textInverse: string;

  // Border colors
  border: string;
  borderSecondary: string;
  borderFocus: string;

  // Interactive states
  hover: string;
  active: string;
  focus: string;
  disabled: string;
  disabledForeground: string;

  // Component-specific colors
  muted: string;
  mutedForeground: string;
  destructive: string;
  destructiveForeground: string;
  ring: string;
  input: string;
  inputForeground: string;
  card: string;
  cardForeground: string;
  popover: string;
  popoverForeground: string;

  // Shadow and effects
  shadow: string;
  shadowSecondary: string;
  overlay: string;

  // Chart and data visualization colors
  chart1: string;
  chart2: string;
  chart3: string;
  chart4: string;
  chart5: string;
}

const lightTheme: ThemeColors = {
  // Core brand colors
  primary: '#2563eb', // Professional blue-600
  primaryForeground: '#ffffff',
  secondary: '#4f46e5', // Professional indigo-600
  secondaryForeground: '#ffffff',
  accent: '#7c3aed', // Professional violet-600
  accentForeground: '#ffffff',
  neutral: '#6b7280', // Professional gray-500

  // Semantic colors
  success: '#059669', // Professional emerald-600
  successForeground: '#ffffff',
  warning: '#d97706', // Professional amber-600
  warningForeground: '#ffffff',
  error: '#dc2626', // Professional red-600
  errorForeground: '#ffffff',
  info: '#0ea5e9', // Professional sky-500
  infoForeground: '#ffffff',

  // Background colors
  background: '#ffffff', // Pure white
  foreground: '#111827', // Professional gray-900
  surface: '#f9fafb', // Professional gray-50
  surfaceSecondary: '#f3f4f6', // Professional gray-100
  surfaceTertiary: '#e5e7eb', // Professional gray-200

  // Text colors
  text: '#111827', // Professional gray-900
  textSecondary: '#6b7280', // Professional gray-500
  textMuted: '#9ca3af', // Professional gray-400
  textInverse: '#ffffff',

  // Border colors
  border: '#e5e7eb', // Professional gray-200
  borderSecondary: '#d1d5db', // Professional gray-300
  borderFocus: '#2563eb', // Professional blue-600

  // Interactive states
  hover: '#f1f5f9', // Professional slate-100
  active: '#e2e8f0', // Professional slate-200
  focus: '#dbeafe', // Professional blue-100
  disabled: '#f8fafc', // Professional slate-50
  disabledForeground: '#cbd5e1', // Professional slate-300

  // Component-specific colors
  muted: '#f1f5f9', // Professional slate-100
  mutedForeground: '#64748b', // Professional slate-500
  destructive: '#dc2626', // Professional red-600
  destructiveForeground: '#ffffff',
  ring: '#2563eb', // Professional blue-600
  input: '#ffffff',
  inputForeground: '#111827',
  card: '#ffffff',
  cardForeground: '#111827',
  popover: '#ffffff',
  popoverForeground: '#111827',

  // Shadow and effects
  shadow: 'rgba(0, 0, 0, 0.1)', // Light shadow
  shadowSecondary: 'rgba(0, 0, 0, 0.05)', // Lighter shadow
  overlay: 'rgba(0, 0, 0, 0.5)', // Modal overlay

  // Chart and data visualization colors
  chart1: '#2563eb', // Blue
  chart2: '#059669', // Emerald
  chart3: '#d97706', // Amber
  chart4: '#7c3aed', // Violet
  chart5: '#dc2626', // Red
};

const darkTheme: ThemeColors = {
  // Core brand colors
  primary: '#3b82f6', // Professional blue-500
  primaryForeground: '#ffffff',
  secondary: '#6366f1', // Professional indigo-500
  secondaryForeground: '#ffffff',
  accent: '#8b5cf6', // Professional violet-500
  accentForeground: '#ffffff',
  neutral: '#9ca3af', // Professional gray-400

  // Semantic colors
  success: '#10b981', // Professional emerald-500
  successForeground: '#ffffff',
  warning: '#f59e0b', // Professional amber-500
  warningForeground: '#111827',
  error: '#ef4444', // Professional red-500
  errorForeground: '#ffffff',
  info: '#0ea5e9', // Professional sky-500
  infoForeground: '#ffffff',

  // Background colors
  background: '#111827', // Professional gray-900
  foreground: '#f9fafb', // Professional gray-50
  surface: '#1f2937', // Professional gray-800
  surfaceSecondary: '#374151', // Professional gray-700
  surfaceTertiary: '#4b5563', // Professional gray-600

  // Text colors
  text: '#f9fafb', // Professional gray-50
  textSecondary: '#d1d5db', // Professional gray-300
  textMuted: '#9ca3af', // Professional gray-400
  textInverse: '#111827',

  // Border colors
  border: '#4b5563', // Professional gray-600
  borderSecondary: '#6b7280', // Professional gray-500
  borderFocus: '#3b82f6', // Professional blue-500

  // Interactive states
  hover: '#374151', // Professional gray-700
  active: '#4b5563', // Professional gray-600
  focus: '#1e3a8a', // Professional blue-800
  disabled: '#1f2937', // Professional gray-800
  disabledForeground: '#6b7280', // Professional gray-500

  // Component-specific colors
  muted: '#374151', // Professional gray-700
  mutedForeground: '#9ca3af', // Professional gray-400
  destructive: '#ef4444', // Professional red-500
  destructiveForeground: '#ffffff',
  ring: '#3b82f6', // Professional blue-500
  input: '#1f2937',
  inputForeground: '#f9fafb',
  card: '#1f2937',
  cardForeground: '#f9fafb',
  popover: '#1f2937',
  popoverForeground: '#f9fafb',

  // Shadow and effects
  shadow: 'rgba(0, 0, 0, 0.3)', // Stronger shadow for dark mode
  shadowSecondary: 'rgba(0, 0, 0, 0.2)', // Medium shadow
  overlay: 'rgba(0, 0, 0, 0.7)', // Modal overlay

  // Chart and data visualization colors
  chart1: '#3b82f6', // Blue
  chart2: '#10b981', // Emerald
  chart3: '#f59e0b', // Amber
  chart4: '#8b5cf6', // Violet
  chart5: '#ef4444', // Red
};

interface ThemeState {
  theme: Theme;
  variant: ThemeVariant;
  colors: ThemeColors;
  isDark: boolean;
  isSystemTheme: boolean;

  // Core theme actions
  setTheme: (theme: Theme) => void;
  setVariant: (variant: ThemeVariant) => void;
  toggleTheme: () => void;

  // Utility functions
  getCSSVariables: () => Record<string, string>;
  applyCSSVariables: () => void;
  getThemeClass: () => string;

  // System preference detection
  initializeSystemTheme: () => void;
  handleSystemThemeChange: (e: MediaQueryListEvent) => void;
}

// System theme detection
let systemThemeMediaQuery: MediaQueryList | null = null;

export const useThemeStore = create<ThemeState>()(
  devtools(
    persist(
      (set, get) => ({
        theme: 'system',
        variant: 'default',
        colors: lightTheme,
        isDark: false,
        isSystemTheme: true,

        setTheme: (theme: Theme) => {
          const isDark =
            theme === 'dark' ||
            (theme === 'system' &&
              window.matchMedia('(prefers-color-scheme: dark)').matches);

          const isSystemTheme = theme === 'system';

          set({
            theme,
            colors: isDark ? darkTheme : lightTheme,
            isDark,
            isSystemTheme,
          });

          // Update document class for Tailwind dark mode
          document.documentElement.classList.toggle('dark', isDark);

          // Apply CSS variables
          get().applyCSSVariables();
        },

        setVariant: (variant: ThemeVariant) => {
          set({ variant });
          // Apply variant-specific modifications if needed
          get().applyCSSVariables();
        },

        toggleTheme: () => {
          const { theme } = get();
          const newTheme = theme === 'light' ? 'dark' : 'light';
          get().setTheme(newTheme);
        },

        getCSSVariables: () => {
          const { colors } = get();
          return {
            // Core brand colors
            '--color-primary': colors.primary,
            '--color-primary-foreground': colors.primaryForeground,
            '--color-secondary': colors.secondary,
            '--color-secondary-foreground': colors.secondaryForeground,
            '--color-accent': colors.accent,
            '--color-accent-foreground': colors.accentForeground,
            '--color-neutral': colors.neutral,

            // Semantic colors
            '--color-success': colors.success,
            '--color-success-foreground': colors.successForeground,
            '--color-warning': colors.warning,
            '--color-warning-foreground': colors.warningForeground,
            '--color-error': colors.error,
            '--color-error-foreground': colors.errorForeground,
            '--color-info': colors.info,
            '--color-info-foreground': colors.infoForeground,

            // Background colors
            '--color-background': colors.background,
            '--color-foreground': colors.foreground,
            '--color-surface': colors.surface,
            '--color-surface-secondary': colors.surfaceSecondary,
            '--color-surface-tertiary': colors.surfaceTertiary,

            // Text colors
            '--color-text': colors.text,
            '--color-text-secondary': colors.textSecondary,
            '--color-text-muted': colors.textMuted,
            '--color-text-inverse': colors.textInverse,

            // Border colors
            '--color-border': colors.border,
            '--color-border-secondary': colors.borderSecondary,
            '--color-border-focus': colors.borderFocus,

            // Interactive states
            '--color-hover': colors.hover,
            '--color-active': colors.active,
            '--color-focus': colors.focus,
            '--color-disabled': colors.disabled,
            '--color-disabled-foreground': colors.disabledForeground,

            // Component-specific colors
            '--color-muted': colors.muted,
            '--color-muted-foreground': colors.mutedForeground,
            '--color-destructive': colors.destructive,
            '--color-destructive-foreground': colors.destructiveForeground,
            '--color-ring': colors.ring,
            '--color-input': colors.input,
            '--color-input-foreground': colors.inputForeground,
            '--color-card': colors.card,
            '--color-card-foreground': colors.cardForeground,
            '--color-popover': colors.popover,
            '--color-popover-foreground': colors.popoverForeground,

            // Shadow and effects
            '--color-shadow': colors.shadow,
            '--color-shadow-secondary': colors.shadowSecondary,
            '--color-overlay': colors.overlay,

            // Chart colors
            '--color-chart-1': colors.chart1,
            '--color-chart-2': colors.chart2,
            '--color-chart-3': colors.chart3,
            '--color-chart-4': colors.chart4,
            '--color-chart-5': colors.chart5,
          };
        },

        applyCSSVariables: () => {
          const variables = get().getCSSVariables();
          const root = document.documentElement;

          Object.entries(variables).forEach(([property, value]) => {
            root.style.setProperty(property, value);
          });
        },

        getThemeClass: () => {
          const { isDark, variant } = get();
          const classes = [isDark ? 'dark' : 'light'];

          if (variant !== 'default') {
            classes.push(`theme-${variant}`);
          }

          return classes.join(' ');
        },

        initializeSystemTheme: () => {
          if (typeof window === 'undefined') return;

          systemThemeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

          // Set initial theme based on system preference
          const { theme } = get();
          if (theme === 'system') {
            get().setTheme('system');
          }

          // Listen for system theme changes
          systemThemeMediaQuery.addEventListener('change', get().handleSystemThemeChange);
        },

        handleSystemThemeChange: (e: MediaQueryListEvent) => {
          const { theme } = get();
          if (theme === 'system') {
            get().setTheme('system');
          }
        },
      }),
      {
        name: 'theme-store',
        onRehydrateStorage: () => state => {
          // Apply theme and initialize system detection on hydration
          if (state) {
            state.initializeSystemTheme();
            state.setTheme(state.theme);
          }
        },
      }
    ),
    {
      name: 'theme-store',
    }
  )
);

// Cleanup function for system theme listener
export const cleanupThemeStore = () => {
  if (systemThemeMediaQuery) {
    const { handleSystemThemeChange } = useThemeStore.getState();
    systemThemeMediaQuery.removeEventListener('change', handleSystemThemeChange);
    systemThemeMediaQuery = null;
  }
};

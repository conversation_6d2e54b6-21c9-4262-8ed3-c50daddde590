import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useThemeStore, cleanupThemeStore } from '../stores/themeStore';
import type { Theme, ThemeVariant, ThemeColors } from '../stores/themeStore';

interface ThemeContextValue {
  theme: Theme;
  variant: ThemeVariant;
  colors: ThemeColors;
  isDark: boolean;
  isSystemTheme: boolean;
  setTheme: (theme: Theme) => void;
  setVariant: (variant: ThemeVariant) => void;
  toggleTheme: () => void;
  getCSSVariables: () => Record<string, string>;
  applyCSSVariables: () => void;
  getThemeClass: () => string;
}

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

export interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: Theme;
  defaultVariant?: ThemeVariant;
  enableSystemDetection?: boolean;
  enableCSSVariables?: boolean;
  storageKey?: string;
}

export function ThemeProvider({
  children,
  defaultTheme = 'system',
  defaultVariant = 'default',
  enableSystemDetection = true,
  enableCSSVariables = true,
  storageKey = 'theme-store',
}: ThemeProviderProps) {
  const {
    theme,
    variant,
    colors,
    isDark,
    isSystemTheme,
    setTheme,
    setVariant,
    toggleTheme,
    getCSSVariables,
    applyCSSVariables,
    getThemeClass,
    initializeSystemTheme,
  } = useThemeStore();

  // Initialize theme on mount
  useEffect(() => {
    // Initialize system theme detection if enabled
    if (enableSystemDetection) {
      initializeSystemTheme();
    }

    // Set default theme if no theme is stored
    if (!theme || theme === 'system') {
      setTheme(defaultTheme);
    }

    // Set default variant if no variant is stored
    if (!variant || variant === 'default') {
      setVariant(defaultVariant);
    }

    // Apply CSS variables if enabled
    if (enableCSSVariables) {
      applyCSSVariables();
    }

    // Cleanup on unmount
    return () => {
      if (enableSystemDetection) {
        cleanupThemeStore();
      }
    };
  }, [
    defaultTheme,
    defaultVariant,
    enableSystemDetection,
    enableCSSVariables,
    theme,
    variant,
    setTheme,
    setVariant,
    applyCSSVariables,
    initializeSystemTheme,
  ]);

  // Apply CSS variables when colors change
  useEffect(() => {
    if (enableCSSVariables) {
      applyCSSVariables();
    }
  }, [colors, enableCSSVariables, applyCSSVariables]);

  // Apply theme class to document element
  useEffect(() => {
    const themeClass = getThemeClass();
    const root = document.documentElement;
    
    // Remove existing theme classes
    root.classList.remove('light', 'dark', 'theme-high-contrast', 'theme-colorblind-friendly');
    
    // Add current theme classes
    themeClass.split(' ').forEach(className => {
      if (className) {
        root.classList.add(className);
      }
    });
  }, [getThemeClass]);

  const contextValue: ThemeContextValue = {
    theme,
    variant,
    colors,
    isDark,
    isSystemTheme,
    setTheme,
    setVariant,
    toggleTheme,
    getCSSVariables,
    applyCSSVariables,
    getThemeClass,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme(): ThemeContextValue {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Higher-order component for theme-aware components
export function withTheme<P extends object>(
  Component: React.ComponentType<P & { theme: ThemeContextValue }>
) {
  return function ThemedComponent(props: P) {
    const theme = useTheme();
    return <Component {...props} theme={theme} />;
  };
}

// Hook for accessing specific theme colors
export function useThemeColors() {
  const { colors } = useTheme();
  return colors;
}

// Hook for theme-aware styling
export function useThemeStyles() {
  const { colors, isDark, variant } = useTheme();
  
  return {
    colors,
    isDark,
    variant,
    getColor: (colorKey: keyof ThemeColors) => colors[colorKey],
    getContrastColor: (backgroundColor: string) => {
      // Simple contrast calculation - in production, you might want a more sophisticated algorithm
      const hex = backgroundColor.replace('#', '');
      const r = parseInt(hex.substr(0, 2), 16);
      const g = parseInt(hex.substr(2, 2), 16);
      const b = parseInt(hex.substr(4, 2), 16);
      const brightness = (r * 299 + g * 587 + b * 114) / 1000;
      return brightness > 128 ? colors.text : colors.textInverse;
    },
    applyThemeStyles: (element: HTMLElement) => {
      const variables = useThemeStore.getState().getCSSVariables();
      Object.entries(variables).forEach(([property, value]) => {
        element.style.setProperty(property, value);
      });
    },
  };
}

// Component for applying theme styles to specific elements
export interface ThemeStylesProps {
  children: ReactNode;
  className?: string;
  style?: React.CSSProperties;
  applyVariables?: boolean;
}

export function ThemeStyles({
  children,
  className = '',
  style = {},
  applyVariables = true,
}: ThemeStylesProps) {
  const { getCSSVariables } = useTheme();
  const elementRef = React.useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (applyVariables && elementRef.current) {
      const variables = getCSSVariables();
      Object.entries(variables).forEach(([property, value]) => {
        elementRef.current!.style.setProperty(property, value);
      });
    }
  }, [getCSSVariables, applyVariables]);

  return (
    <div ref={elementRef} className={className} style={style}>
      {children}
    </div>
  );
}

// Utility function to create theme-aware CSS-in-JS styles
export function createThemeStyles(colors: ThemeColors) {
  return {
    primary: {
      backgroundColor: colors.primary,
      color: colors.primaryForeground,
    },
    secondary: {
      backgroundColor: colors.secondary,
      color: colors.secondaryForeground,
    },
    surface: {
      backgroundColor: colors.surface,
      color: colors.text,
    },
    card: {
      backgroundColor: colors.card,
      color: colors.cardForeground,
      border: `1px solid ${colors.border}`,
    },
    input: {
      backgroundColor: colors.input,
      color: colors.inputForeground,
      border: `1px solid ${colors.border}`,
    },
    button: {
      primary: {
        backgroundColor: colors.primary,
        color: colors.primaryForeground,
        border: `1px solid ${colors.primary}`,
      },
      secondary: {
        backgroundColor: colors.secondary,
        color: colors.secondaryForeground,
        border: `1px solid ${colors.secondary}`,
      },
      outline: {
        backgroundColor: 'transparent',
        color: colors.primary,
        border: `1px solid ${colors.border}`,
      },
      ghost: {
        backgroundColor: 'transparent',
        color: colors.text,
        border: '1px solid transparent',
      },
    },
  };
}

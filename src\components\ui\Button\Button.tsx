import React from 'react';
import { useTheme } from '../../../providers/ThemeProvider';
import { cn } from '../../../utils/cn';

export interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
  'data-testid'?: string;
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  fullWidth = false,
  onClick,
  type = 'button',
  className = '',
  'data-testid': testId,
  ...props
}) => {
  const { colors } = useTheme();

  const baseClasses = cn(
    'inline-flex items-center justify-center font-medium rounded-theme transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none',
    'active:scale-95 transform-gpu',
    fullWidth && 'w-full'
  );

  const sizeClasses = {
    sm: 'h-8 px-3 text-sm',
    md: 'h-10 px-4 text-base',
    lg: 'h-12 px-6 text-lg',
  };

  const variantClasses = {
    primary: cn(
      'bg-primary text-primary-foreground border border-primary',
      'hover:bg-primary/90 hover:border-primary/90',
      'focus:ring-primary/20'
    ),
    secondary: cn(
      'bg-secondary text-secondary-foreground border border-secondary',
      'hover:bg-secondary/90 hover:border-secondary/90',
      'focus:ring-secondary/20'
    ),
    outline: cn(
      'bg-transparent text-primary border border-border',
      'hover:bg-surface hover:text-primary',
      'focus:ring-primary/20'
    ),
    ghost: cn(
      'bg-transparent text-text border border-transparent',
      'hover:bg-hover hover:text-text',
      'focus:ring-primary/20'
    ),
    destructive: cn(
      'bg-destructive text-destructive-foreground border border-destructive',
      'hover:bg-destructive/90 hover:border-destructive/90',
      'focus:ring-destructive/20'
    ),
  };

  const buttonClasses = cn(
    baseClasses,
    sizeClasses[size],
    variantClasses[variant],
    loading && 'cursor-wait',
    className
  );

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (!disabled && !loading && onClick) {
      onClick(event);
    }
  };

  return (
    <button
      type={type}
      className={buttonClasses}
      disabled={disabled || loading}
      onClick={handleClick}
      data-testid={testId}
      {...props}
    >
      {loading && (
        <svg
          className="animate-spin -ml-1 mr-2 h-4 w-4 text-current"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      {children}
    </button>
  );
};

export default Button;
